import subprocess
import sys
import time
import asyncio
from pathlib import Path

from watchfiles import watch


def run_server(process_args):
    """Run the server in a subprocess."""
    print(f"🚀 Starting server with: {' '.join(process_args)}")
    return subprocess.Popen(process_args)


def main():
    """Main function to watch for file changes and restart the server."""
    # On Windows, set the event loop policy for Playwright compatibility
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    # Ensure the command is run from the BE directory
    script_path = Path(__file__).resolve()
    project_root = script_path.parent
    api_main_path = project_root / "API" / "main.py"

    if not api_main_path.exists():
        print(f"❌ Error: Could not find '{api_main_path}'. Make sure you are running this script from the 'BE' directory.")
        sys.exit(1)

    process_args = [sys.executable, str(api_main_path)]
    server_process = run_server(process_args)

    # Directories to watch for changes
    watch_paths = [
        project_root / "API",
        project_root / "Scraper",
        project_root / "AITextEnhancer",
    ]

    print(f"👀 Watching for changes in: {[str(p.relative_to(project_root)) for p in watch_paths]}")

    try:
        for changes in watch(*watch_paths):
            print("🔄 Detected changes:")
            for change, path in changes:
                print(f"  - {change.name.capitalize()}: {Path(path).relative_to(project_root)}")
            
            print("🛑 Stopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("⚠️ Server did not terminate gracefully, killing it.")
                server_process.kill()
            
            print("----------------------------------------------------")
            time.sleep(1) # Brief pause before restarting
            server_process = run_server(process_args)

    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received, stopping server...")
        server_process.terminate()
        server_process.wait()
        print("👋 Server stopped. Goodbye!")

if __name__ == "__main__":
    main()