"""
Main MetruyenScraper Class
Orchestrates the entire scraping process with all components
"""


from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from loguru import logger

from .config_manager import ConfigManager
from .scraper_engine import ScraperEngine
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, ScrapingError
from .data_processor import DataProcessor


class MetruyenScraper:
    """
    Main scraper class for Vietnamese novel websites.

    This class orchestrates all scraping components including:
    - Browser automation with anti-detection measures
    - Error handling and retry logic
    - Data processing and export
    - Configuration management

    Designed specifically for webtruyen.diendantruyen.com but can be adapted
    for other Vietnamese novel sites by modifying the configuration.

    Example:
        async with MetruyenScraper() as scraper:
            data = await scraper.scrape_story_info(url)
            exported_files = scraper.export_data()
    """

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the scraper with configuration.

        Args:
            config_path: Path to the YAML configuration file containing
                        selectors, delays, and other scraping settings
        """
        self.config = ConfigManager(config_path)
        self.error_handler = ErrorHandler(self.config)
        self.data_processor = DataProcessor(self.config)
        self.scraper_engine: Optional[ScraperEngine] = None

        logger.info("MetruyenScraper initialized")
    
    async def __aenter__(self):
        """Context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Start the scraper engine"""
        self.scraper_engine = ScraperEngine(self.config.config_path)
        await self.scraper_engine.start()
        logger.info("MetruyenScraper started")
    
    async def close(self) -> None:
        """Close the scraper engine"""
        if self.scraper_engine:
            await self.scraper_engine.close()
        logger.info("MetruyenScraper closed")
    
    @property
    def retry_on_error(self):
        """Expose retry decorator"""
        return self.error_handler.retry_on_error
    
    async def scrape_story_info(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape story information from a given URL."""
        if not self.scraper_engine:
            raise RuntimeError("Scraper not started. Use context manager.")

        @self.retry_on_error(retry_on=[ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR])
        async def _scrape_with_retry():
            return await self.scraper_engine.scrape_story_info(url)

        try:
            data = await _scrape_with_retry()
            self.data_processor.add_data(data)
            return data
        except ScrapingError as e:
            logger.error(f"Failed to scrape story info from {url}: {e}")
            return None
    
    async def scrape_urls(self, urls: List[str], target_name: str = "webtruyen", 
                         max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """Scrape multiple URLs concurrently"""
        if not urls:
            return []
        
        logger.info(f"Starting to scrape {len(urls)} URLs")
        
        # This is no longer async, so we'll just loop through them
        # For true concurrency, a thread pool would be needed here.
        successful_results = []
        for url in urls:
            try:
                result = await self.scrape_story_info(url)
                if result:
                    successful_results.append(result)
            except Exception as e:
                logger.error(f"Error scraping {url}: {e}")

        logger.info(f"Successfully scraped {len(successful_results)}/{len(urls)} URLs")
        return successful_results
    
    def _display_chapter_list(self, chapters: List[Dict]) -> None:
        """Display numbered list of chapters for user selection"""
        print("\n" + "="*80)
        print("📚 AVAILABLE CHAPTERS")
        print("="*80)
        
        for i, chapter in enumerate(chapters, 1):
            chapter_title = chapter.get('title', f'Chapter {i}')
            # Truncate long titles for better display
            display_title = chapter_title[:70] + "..." if len(chapter_title) > 70 else chapter_title
            print(f"{i:3d}. {display_title}")
        
        print("="*80)
        print(f"Total: {len(chapters)} chapters available")
        print("="*80 + "\n")

    def _get_user_chapter_range(self, chapters: List[Dict]) -> tuple[int, int]:
        """Get chapter range from user input with validation"""
        total_chapters = len(chapters)
        
        while True:
            try:
                print(f"📖 Select chapter range to scrape (1-{total_chapters}):")
                print("💡 Press Enter to scrape all chapters")
                
                start_input = input(f"🔢 Start chapter (1-{total_chapters}): ").strip()
                if not start_input:
                    return 1, total_chapters
                
                start_chapter = int(start_input)
                
                end_input = input(f"🔢 End chapter ({start_chapter}-{total_chapters}): ").strip()
                if not end_input:
                    end_chapter = total_chapters
                else:
                    end_chapter = int(end_input)
                
                if self._validate_chapter_range(start_chapter, end_chapter, total_chapters):
                    return start_chapter, end_chapter
                else:
                    print("❌ Invalid range! Please try again.\n")
                    
            except ValueError:
                print("❌ Please enter valid numbers!\n")
            except KeyboardInterrupt:
                print("\n🛑 Operation cancelled by user")
                raise
            except EOFError:
                print("\n🛑 Input cancelled")
                raise

    def _validate_chapter_range(self, start: int, end: int, total_chapters: int) -> bool:
        """Validate chapter range input"""
        if start < 1 or start > total_chapters:
            print(f"❌ Start chapter must be between 1 and {total_chapters}")
            return False
        
        if end < start or end > total_chapters:
            print(f"❌ End chapter must be between {start} and {total_chapters}")
            return False
        
        return True

    async def scrape_story_info_comprehensive(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape comprehensive story information including all chapters."""
        if not self.scraper_engine:
            raise RuntimeError("Scraper not started. Use context manager.")

        logger.info(f"Starting comprehensive scrape for story: {url}")
        try:
            story_info = await self.scraper_engine.scrape_story_info(url)
            if not story_info:
                logger.error(f"Could not retrieve basic info for story: {url}")
                return None

            # Check if scraping failed and returned an error dictionary
            if 'error' in story_info:
                logger.error(f"Scraper engine returned error for {url}: {story_info['error']}")
                return None

            # Check if chapters key exists before accessing it
            if 'chapters' not in story_info:
                logger.error(f"No chapters found in story info for {url}")
                return None

            chapters = await self.scraper_engine.batch_scrape_chapters(url, story_info['chapters'])
            story_info['chapters'] = chapters

            logger.success(f"Finished comprehensive scrape for story: {story_info.get('title', url)}")
            self.data_processor.add_data(story_info)
            return story_info
        except ScrapingError as e:
            logger.error(f"Comprehensive scraping failed for {url}: {e}")
            return None
        except KeyError as e:
            logger.error(f"Missing required key in story data for {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during comprehensive scraping for {url}: {e}")
            return None
    
    async def scrape_with_pagination(self, base_url: str, target_name: str = "webtruyen",
                                   max_pages: Optional[int] = None) -> List[Dict[str, Any]]:
        """Scrape content with pagination support"""
        logger.info(f"Starting pagination scraping from: {base_url}")
        
        all_results = []
        current_url = base_url
        page_count = 0
        
        while current_url and (max_pages is None or page_count < max_pages):
            page_count += 1
            logger.info(f"Scraping page {page_count}: {current_url}")
            
            page_data = await self.scrape_story_info(current_url)
            if not page_data:
                logger.warning(f"Failed to scrape page {page_count}")
                break
            
            all_results.append(page_data)
            
            # Look for next page URL
            navigation = page_data.get('navigation', {})
            current_url = navigation.get('next_chapter')  # or next_page depending on site
            
            if not current_url:
                logger.info("No more pages found")
                break
        
        logger.info(f"Completed pagination scraping: {len(all_results)} pages")
        return all_results
    
    def export_data(self, output_dir: str = "output") -> Dict[str, str]:
        """Export all scraped data"""
        return self.data_processor.export_data(output_dir)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        data_stats = self.data_processor.get_statistics()
        error_stats = self.error_handler.get_error_statistics()
        
        return {
            'data_statistics': data_stats,
            'error_statistics': error_stats,
            'failed_urls': self.error_handler.get_failed_urls()
        }
    
    def clear_data(self) -> None:
        """Clear all cached data and statistics"""
        self.data_processor.clear_cache()
        self.error_handler.clear_error_stats()
        logger.info("All data and statistics cleared")
    
    async def test_target_site(self, test_url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Test scraping capabilities on target site"""
        logger.info(f"Testing scraping on: {test_url}")
        
        test_results = {
            'url': test_url,
            'target': target_name,
            'success': False,
            'data_extracted': False,
            'content_locked': False,
            'navigation_found': False,
            'errors': []
        }
        
        try:
            data = await self.scrape_story_info(test_url)
            
            if data:
                test_results['success'] = True
                test_results['data_extracted'] = bool(data.get('content'))
                test_results['content_locked'] = data.get('is_locked', False)
                test_results['navigation_found'] = bool(data.get('navigation', {}).get('chapters'))
                
                logger.info("Test scraping successful")
            else:
                test_results['errors'].append("No data returned")
                logger.warning("Test scraping returned no data")
        
        except Exception as e:
            test_results['errors'].append(str(e))
            logger.error(f"Test scraping failed: {e}")
        
        return test_results
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # Check required configuration sections
            required_sections = ['scraper', 'targets', 'output']
            for section in required_sections:
                if not self.config.get(section):
                    validation_results['errors'].append(f"Missing required section: {section}")
                    validation_results['valid'] = False
            
            # Check target configuration
            targets = self.config.get('targets', {})
            if not targets:
                validation_results['errors'].append("No target websites configured")
                validation_results['valid'] = False
            
            # Check browser configuration
            browser_config = self.config.get('scraper.browser', {})
            if not browser_config:
                validation_results['warnings'].append("No browser configuration found, using defaults")
            
            logger.info(f"Configuration validation: {'PASSED' if validation_results['valid'] else 'FAILED'}")
            
        except Exception as e:
            validation_results['errors'].append(f"Configuration validation error: {e}")
            validation_results['valid'] = False
        
        return validation_results
