logging:
  file: scraper.log
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}'
  level: INFO
output:
  csv:
    delimiter: ','
    encoding: utf-8-sig
  formats:
  - json
  # - csv
  json:
    encoding: utf-8
    pretty: true
scraper:
  browser:
    headless: true
    timeout: 30000
    viewport:
      height: 1080
      width: 1920
  retry:
    backoff_factor: 2
    max_attempts: 3
    timeout: 60
  stealth:
    delays:
      max_delay: 5
      min_delay: 2
      page_load_delay: 3
    proxy:
      enabled: false
      list: []
      rotation: false
    user_agents:
    - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko)
      Chrome/120.0.0.0 Safari/537.36
    - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like
      Gecko) Chrome/120.0.0.0 Safari/537.36
    - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0
      Safari/537.36
    - Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
    - Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0
targets:
  webtruyen:
    base_url: https://webtruyen.diendantruyen.com
    selectors:
      # Main content selectors (prioritized order)
      content: '.chapter-content, .story-content, .content-area p'
      title: 'h1, .entry-title, .chapter-title, h3'

      # Navigation selectors
      next_chapter: 'a[id="next-link"], a[href*="chuong-"]:contains("Sau"), .nav-next a'
      prev_chapter: 'a[id="prev-link"], a[href*="chuong-"]:contains("Trước"), .nav-previous a'

      # Chapter list selectors for story pages
      chapter_list: '#chapter-list-tab, .chapter-list, .story-chapters'
      chapter_link: 'a[class*="uk-link-toggle"]'

      # Story page selectors
      story_title: '#category-title'
      story_image: 'img[alt*="Ảnh"]'  # Can be empty
      story_author: '.story-info-right li:contains("Tác giả") a'
      story_description: '.tab-story > p'  # Can be empty
      meta_description: 'meta[name="description"]'
      meta_keywords: 'meta[name="keywords"]'
      story_chapters: '#chapter-list-tab, .chapter-list, .story-chapters'
      story_pagination: '.uk-pagination'
      story_pagination_link: '.uk-pagination a'

      # Locked content detection
      locked_content: '.premium-content, .vip-content, .locked-content, .paywall'

    wait_conditions:
    - networkidle
    - domcontentloaded
