"""
Scraping Service

This service provides high-level scraping operations for stories and chapters.
Integrates with the real MetruyenScraper for actual web scraping functionality.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import Depends, Request
from API.utils.logging_config import LoggerMixin
from API.middleware.error_handling import ScrapingError
from API.core.config import get_settings


import logging

async def get_scraper(request: Request):
    if not hasattr(request.app.state, 'scraper') or not request.app.state.scraper:
        raise ScrapingError("Scraping service is not available.")
    
    # Import here to avoid circular imports
    from API.core.app import ensure_scraper_started
    return await ensure_scraper_started(request.app)

class ScrapingService(LoggerMixin):
    _logger = logging.getLogger(__name__)
    """Service for web scraping operations using real MetruyenScraper"""
    
    async def scrape_story_info(
        self,
        story_url: str,
        request: Request,
        max_pages: int = 10,
    ) -> Dict[str, Any]:
        """
        Scrape comprehensive story information and chapter list using refactored scraper.

        This method uses the new specialized scraping modules to extract:
        - Story information (title, description, image, metadata)
        - Complete chapter list with pagination
        - Page information with chapter URLs for future content scraping

        Args:
            story_url: URL of the story page
            request: The FastAPI request object.
            max_pages: Maximum number of pages to scrape for chapters (default: 10)

        Returns:
            Dictionary containing comprehensive story information and chapters organized by pages
        """
        scraper = await get_scraper(request)

        try:
            self.log_info(f"🚀 Starting comprehensive story info scraping from: {story_url}")

            # Use the new comprehensive scraping method
            story_data = await scraper.scrape_story_info_comprehensive(
                story_url
            )

            if not story_data:
                raise ScrapingError("Failed to scrape story page", url=story_url)

            # Extract and organize the data according to our API structure
            result = {
                "url": story_url,
                "title": story_data.get("title", "Unknown Title"),
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "scraped_at": datetime.utcnow().isoformat(),
                    "source_website": "webtruyen",
                    "status": story_data.get("metadata", {}).get("status", "ongoing"),
                    "author": story_data.get("metadata", {}).get("author", "Unknown"),
                    "description": story_data.get("metadata", {}).get("description", ""),
                    "cover_image_url": story_data.get("metadata", {}).get("cover_image_url"),
                    "total_chapters": story_data.get("total_chapters", 0),
                    "total_pages": story_data.get("total_pages", 1),
                    "total_pages_available": story_data.get("total_pages_available", 1),
                    "max_pages_scraped": max_pages
                },
                "chapters": story_data.get("chapters", []),
                "pages": story_data.get("pages", [])
            }

            # Add pagination error if present
            if story_data.get("pagination_error"):
                result["metadata"]["pagination_error"] = story_data["pagination_error"]

            self.log_info(f"✅ Successfully scraped comprehensive story info: {result['title']} "
                         f"({len(result.get('chapters', []))} chapters from {result['metadata']['total_pages']} pages)")

            return result

        except Exception as e:
            self.log_error(f"Error scraping comprehensive story info from {story_url}: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}", url=story_url)
    
    async def scrape_chapter_content(self, chapter_url: str, request: Request) -> Dict[str, Any]:
        """
        Scrape content from a single chapter using real scraper

        Args:
            chapter_url: URL of the chapter page

        Returns:
            Dictionary containing chapter content and metadata
        """
        scraper = await get_scraper(request)

        try:
            self.log_info(f"Scraping chapter content from: {chapter_url}")
            start_time = datetime.utcnow()

            # Use real scraper to get chapter content
            chapter_data = await scraper.scrape_url(chapter_url, "webtruyen")

            if not chapter_data:
                raise ScrapingError("Failed to scrape chapter content", url=chapter_url)

            # Calculate scraping duration
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Extract content and metadata
            content = chapter_data.get("content", "")
            title = chapter_data.get("title", "Unknown Chapter")
            is_locked = chapter_data.get("is_locked", False)
            navigation = chapter_data.get("navigation", {})

            # Build result
            result = {
                "url": chapter_url,
                "title": title,
                "content": content,
                "is_locked": is_locked,
                "scraping_duration": duration,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "scraped_at": end_time.isoformat(),
                    "word_count": len(content.split()) if content else 0,
                    "character_count": len(content) if content else 0,
                    "source_url": chapter_url,
                    "navigation": navigation,
                    "scraping_metadata": chapter_data.get("metadata", {})
                }
            }

            self.log_info(f"Successfully scraped chapter: {title} ({result['metadata']['word_count']} words)")
            return result

        except Exception as e:
            self.log_error(f"Error scraping chapter content from {chapter_url}: {e}")
            raise ScrapingError(f"Failed to scrape chapter content: {e}", url=chapter_url)
    
    async def scrape_multiple_chapters(
        self,
        chapter_urls: List[str],
        max_concurrent: int = 3,
        rate_limit_delay: float = 2.0
    ) -> List[Dict[str, Any]]:
        """
        Scrape content from multiple chapters concurrently using real scraper

        Args:
            chapter_urls: List of chapter URLs to scrape
            max_concurrent: Maximum concurrent scraping operations
            rate_limit_delay: Delay between requests in seconds

        Returns:
            List of chapter data dictionaries
        """
        await self.initialize()

        try:
            self.log_info(f"Starting batch scraping of {len(chapter_urls)} chapters")

            # Scrape chapters one by one with rate limiting
            results = []
            failed_urls = []

            for i, chapter_url in enumerate(chapter_urls):
                try:
                    self.log_info(f"Scraping chapter {i+1}/{len(chapter_urls)}: {chapter_url}")

                    # Add rate limiting delay
                    if i > 0:
                        await asyncio.sleep(rate_limit_delay)

                    # Scrape individual chapter
                    chapter_data = await self._scraper.scrape_url(chapter_url, "webtruyen")

                    if chapter_data:
                        content = chapter_data.get("content", "")
                        title = chapter_data.get("title", "Unknown Chapter")
                        url = chapter_data.get("url", chapter_url)
                        is_locked = chapter_data.get("is_locked", False)
                        navigation = chapter_data.get("navigation", {})

                        result = {
                            "url": url,
                            "title": title,
                            "content": content,
                            "is_locked": is_locked,
                            "scraping_duration": 0,  # Not tracked in batch mode
                            "timestamp": datetime.utcnow().isoformat(),
                            "metadata": {
                                "scraped_at": datetime.utcnow().isoformat(),
                                "word_count": len(content.split()) if content else 0,
                                "character_count": len(content) if content else 0,
                                "source_url": url,
                                "navigation": navigation,
                                "scraping_metadata": chapter_data.get("metadata", {})
                            }
                        }
                        results.append(result)
                        self.log_info(f"✅ Chapter {i+1} scraped successfully: {len(content)} characters")
                    else:
                        failed_urls.append(chapter_url)
                        self.log_error(f"❌ Chapter {i+1} failed: No data returned")

                except Exception as e:
                    failed_urls.append(chapter_url)
                    self.log_error(f"❌ Chapter {i+1} failed: {e}")

            success_count = len(results)
            failed_count = len(failed_urls)

            self.log_info(f"Batch scraping completed: {success_count} success, {failed_count} failed")

            return results

        except Exception as e:
            self.log_error(f"Error in batch chapter scraping: {e}")
            raise ScrapingError(f"Batch scraping failed: {e}")
    
    async def test_connection(self) -> str:
        """Test scraping service connection and functionality"""
        try:
            # Import here to avoid circular imports
            from API.core.app import ensure_scraper_started
            from fastapi import Request
            
            # Create a mock request to test the scraper
            class MockApp:
                def __init__(self):
                    self.state = type('obj', (object,), {})()
            
            class MockRequest:
                def __init__(self):
                    self.app = MockApp()
            
            mock_request = MockRequest()
            
            # Try to get the scraper through the normal flow
            scraper = await get_scraper(mock_request)
            
            if scraper:
                return "Scraping service connected and ready"
            else:
                return "Scraping service initialization failed"

        except Exception as e:
            self.log_error(f"Connection test failed: {e}")
            return f"Connection test failed: {e}"

    async def get_scraping_stats(self) -> Dict[str, Any]:
        """Get scraping service statistics"""
        stats = {
            "initialized": self._initialized,
            "scraper_path": str(self._scraper_path),
            "max_concurrent": self.settings.max_concurrent_scraping,
            "rate_limit_delay": self.settings.scraping_delay_min,
            "timeout": self.settings.scraping_timeout
        }

        if self._scraper and self._initialized:
            try:
                scraper_stats = await self._scraper.get_statistics()
                stats.update(scraper_stats)
            except Exception as e:
                self.log_error(f"Failed to get scraper statistics: {e}")
                stats["scraper_stats_error"] = str(e)

        return stats
    
    def __del__(self):
        """Cleanup on destruction"""
        if self._initialized:
            try:
                # Try to cleanup if event loop is available
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup())
                else:
                    loop.run_until_complete(self.cleanup())
            except:
                pass  # Ignore cleanup errors during destruction


# ============================================================================
# Service Factory
# ============================================================================

_scraping_service_instance: Optional[ScrapingService] = None


def get_scraping_service() -> ScrapingService:
    """Get singleton scraping service instance"""
    global _scraping_service_instance
    
    if _scraping_service_instance is None:
        _scraping_service_instance = ScrapingService()
    
    return _scraping_service_instance


async def cleanup_scraping_service():
    """Cleanup scraping service on application shutdown"""
    global _scraping_service_instance
    
    if _scraping_service_instance:
        await _scraping_service_instance.cleanup()
        _scraping_service_instance = None
