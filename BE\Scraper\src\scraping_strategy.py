# d:\Personal Projects\Vibe\Webtruyen\BE\Scraper\src\scraping_strategy.py

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from playwright.async_api import Page

class ScrapingStrategy(ABC):
    """Abstract base class for a scraping strategy."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    @abstractmethod
    async def extract_story_info(self, page: Page) -> Dict[str, Any]:
        """Extracts comprehensive story information from the page."""
        pass

    @abstractmethod
    async def determine_total_pages(self, page: Page) -> int:
        """Determines the total number of chapter pages."""
        pass

    @abstractmethod
    async def collect_chapters_from_page(self, page: Page, page_num: int) -> List[Dict[str, str]]:
        """Collects chapter information from a single page."""
        pass

import re
import time
from urllib.parse import urljoin
from loguru import logger

class WebtruyenStrategy(ScrapingStrategy):
    """Scraping strategy for webtruyen.com."""

    async def extract_story_info(self, page: Page, url: str) -> Dict[str, Any]:
        selectors = self.config.get('selectors', {})
        story_info = {
            'url': url,
            'title': '',
            'chapters': [],  # Initialize empty chapters list
            'metadata': {
                'source_website': 'webtruyen',
                'scraped_at': time.time(),
                'status': 'ongoing'
            }
        }

        await self._extract_title(page, story_info, selectors)
        await self._extract_description(page, story_info, selectors)
        await self._extract_cover_image(page, story_info, selectors, url)
        await self._extract_additional_metadata(page, story_info, selectors)

        logger.info(f"📖 Story info extracted: {story_info['title']}")
        return story_info

    async def _extract_title(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            logger.info(f"🔍 Extracting title with selectors: {selectors}")
            if 'story_title' in selectors:
                selector = selectors['story_title']
                logger.info(f"📝 Trying story_title selector: {selector}")
                title_element = await page.query_selector(selector)
                if title_element:
                    title_text = await title_element.inner_text()
                    logger.info(f"✅ Found title: '{title_text}'")
                    story_info['title'] = title_text.strip()
                    return
                else:
                    logger.warning(f"❌ No element found for story_title selector: {selector}")
            if 'title' in selectors:
                selector = selectors['title']
                logger.info(f"📝 Trying fallback title selector: {selector}")
                title_element = await page.query_selector(selector)
                if title_element:
                    title_text = await title_element.inner_text()
                    logger.info(f"✅ Found title with fallback: '{title_text}'")
                    story_info['title'] = title_text.strip()
                    return
                else:
                    logger.warning(f"❌ No element found for title selector: {selector}")
            
            # Fallback to page title if selectors don't work
            logger.info(f"📄 Trying page title as fallback")
            page_title = await page.title()
            if page_title and page_title != '404 - Hội Nhiều Chữ':
                # Extract story title from page title (remove site suffix)
                title_parts = page_title.split(' – ')
                if len(title_parts) > 1:
                    story_info['title'] = title_parts[0].strip()
                else:
                    story_info['title'] = page_title.strip()
                logger.info(f"✅ Extracted title from page title: '{story_info['title']}'")
                return
            
            logger.warning(f"❌ Could not extract title from any source")
            story_info['title'] = "Unknown Title"
        except Exception as e:
            logger.error(f"❌ Failed to extract title: {e}")
            story_info['title'] = "Unknown Title"

    async def _extract_description(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            if 'story_description' in selectors:
                desc_element = await page.query_selector(selectors['story_description'])
                if desc_element:
                    story_info['metadata']['description'] = (await desc_element.inner_text()).strip()
        except Exception as e:
            logger.warning(f"Failed to extract description: {e}")

    async def _extract_cover_image(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str], url: str) -> None:
        try:
            if 'story_image' in selectors:
                img_element = await page.query_selector(selectors['story_image'])
                if img_element:
                    img_src = await img_element.get_attribute('src')
                    if img_src:
                        story_info['metadata']['cover_image_url'] = self._make_absolute_url(img_src, url)
        except Exception as e:
            logger.warning(f"Failed to extract cover image: {e}")

    async def _extract_additional_metadata(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            if 'story_author' in selectors:
                author_element = await page.query_selector(selectors['story_author'])
                if author_element:
                    story_info['metadata']['author'] = (await author_element.inner_text()).strip()
            else:
                story_info['metadata']['author'] = "Unknown"

            page_meta = await self._extract_page_metadata(page, selectors)
            story_info['metadata'].update(page_meta)
        except Exception as e:
            logger.warning(f"Failed to extract additional metadata: {e}")

    async def _extract_page_metadata(self, page: Page, selectors: Dict[str, str]) -> Dict[str, Any]:
        metadata = {}
        try:
            metadata['page_title'] = await page.title()
            if 'meta_description' in selectors:
                desc_element = await page.query_selector(selectors['meta_description'])
                if desc_element:
                    metadata['meta_description'] = await desc_element.get_attribute('content')
            if 'meta_keywords' in selectors:
                keywords_element = await page.query_selector(selectors['meta_keywords'])
                if keywords_element:
                    metadata['keywords'] = await keywords_element.get_attribute('content')
        except Exception as e:
            logger.warning(f"Page metadata extraction failed: {e}")
        return metadata

    async def determine_total_pages(self, page: Page) -> int:
        selectors = self.config.get('selectors', {})
        try:
            if 'story_pagination' in selectors:
                pagination_container = await page.query_selector(selectors['story_pagination'])
                if pagination_container:
                    return await self._extract_max_page_from_pagination(pagination_container)
            return 1
        except Exception as e:
            logger.error(f"Error determining total pages: {e}")
            return 1

    async def _extract_max_page_from_pagination(self, pagination_container) -> int:
        max_page = 1
        try:
            last_links = await pagination_container.query_selector_all('a')
            for link in last_links:
                try:
                    text = (await link.inner_text()).strip().lower()
                    href = await link.get_attribute('href')
                    if any(indicator in text for indicator in ['cuối', 'last', 'end']):
                        if href and 'trang=' in href:
                            match = re.search(r'trang=(\d+)', href)
                            if match:
                                max_page = max(max_page, int(match.group(1)))
                    elif text.isdigit():
                        max_page = max(max_page, int(text))
                except Exception as link_error:
                    logger.warning(f"Error processing pagination link: {link_error}")
        except Exception as e:
            logger.error(f"Error extracting max page from pagination: {e}")
        return max_page

    async def collect_chapters_from_page(self, page: Page, page_num: int) -> List[Dict[str, str]]:
        selectors = self.config.get('selectors', {})
        chapters = []
        try:
            chapter_list_selector = selectors.get('story_chapters') or selectors.get('chapter_list')
            if chapter_list_selector and 'chapter_link' in selectors:
                chapter_list = await page.query_selector(chapter_list_selector)
                if chapter_list:
                    chapter_links = await chapter_list.query_selector_all(selectors['chapter_link'])
                    for link in chapter_links:
                        chapter_info = await self._extract_chapter_info(link, page)
                        if chapter_info:
                            chapters.append(chapter_info)
        except Exception as e:
            logger.error(f"Error extracting chapters from page {page_num}: {e}")
        return chapters

    async def _extract_chapter_info(self, link_element, page: Page) -> Optional[Dict[str, Any]]:
        try:
            href = await link_element.get_attribute('href')
            title = await link_element.inner_text()
            if href and title:
                return {
                    'url': self._make_absolute_url(href, page.url),
                    'title': title.strip()
                }
        except Exception as e:
            logger.warning(f"Error extracting chapter info: {e}")
        return None

    def _make_absolute_url(self, href: str, current_url: str) -> str:
        if href.startswith('http'):
            return href
        elif href.startswith('/'):
            base_url = self.config.get('base_url', '')
            return base_url + href
        else:
            return urljoin(current_url, href)